import {ConfigManager} from "@/world/Config/ConfigManager";
import {IBagInventoryItem, INVENTORY_TYPE_ENUM} from "@/constant/type";
import {generateUUID} from "three/src/math/MathUtils";
import {getCdnLink} from "@/AvatarOrdinalsBrowser/utils";


const IS_CDN = true

export enum ItemType {
  None,
  Axe = 1,
  Pickaxe = 2,
  FishingRod = 3,
}

export type ItemData = {
  name: string;
  description: string;
  id: number;
  glb_url: string;
  icon_url: string;
  type: number;
  position: number[];
  rotation: number[];
  damage: number;
  axe_width: number;
  axe_length: number;
  action_run: string;
  action_walk: string;
  action_idle: string;
  effect_url: string;
  effect_scale: number;
  quality: 1 | 2 | 3 | 4 | 5 | 6 | 7; // 1白，2绿，3蓝，4紫，5黄，6红，7彩
}

export class ItemConfig {
  private static instance: ItemConfig

  private itemDataMap: Map<number, { url: string, data: ItemData | undefined }>

  private constructor() {
    this.itemDataMap = new Map<number, { url: string, data: ItemData | undefined }>()
    ConfigManager.getInstance().downloadConfig('./config/item/_ids.json', (data: { id: number, url: string }[]) => {
      for (let i = 0; i < data.length; i++) {
        const config = data[i]
        this.itemDataMap.set(config.id, {url: config.url, data: undefined})
      }
    }, IS_CDN)
  }

  static getInstance() {
    if (!ItemConfig.instance) {
      ItemConfig.instance = new ItemConfig()
    }
    return ItemConfig.instance
  }

  private loadMapData(config: { url: string, data: ItemData | undefined }, cb: (data: ItemData) => void) {
    ConfigManager.getInstance().downloadConfig(config.url, (data) => {
      const itemData = data as ItemData
      itemData.name = itemData.name || ''
      itemData.description = itemData.description || itemData.name || ''
      itemData.type = itemData.type || ItemType.None
      itemData.glb_url = itemData.glb_url || ''
      itemData.icon_url = itemData.icon_url || ''
      itemData.position = itemData.position || [0, 0, 0]
      itemData.rotation = itemData.rotation || [0, 0, 0]
      itemData.damage = itemData.damage || 0
      itemData.axe_width = itemData.axe_width || 0.1
      itemData.axe_length = itemData.axe_length || 1.3
      itemData.action_run = itemData.action_run || ''
      itemData.action_walk = itemData.action_walk || ''
      itemData.action_idle = itemData.action_idle || ''
      itemData.effect_url = itemData.effect_url || ''
      itemData.effect_scale = itemData.effect_scale || 1
      itemData.quality = itemData.quality || 0
      config.data = itemData
      cb(itemData)
    }, IS_CDN)
  }

  getData(id: number, cb: (data: ItemData | null) => void) {
    if (this.itemDataMap.size === 0) {
      setTimeout(() => {
        this.getData(id, cb)
      }, 500)
      return
    }

    const config = this.itemDataMap.get(id)
    if (config) {
      const data = config.data
      if (data) {
        cb(data)
      } else {
        this.loadMapData(config, cb)
      }
    } else {
      if (id !== 0) {
        console.error('not found item config id: ' + id)
      }
      cb(null)
    }
  }

  testHandItem(type: ItemType, oldInventoryList: IBagInventoryItem[], cb: (newInventoryList: IBagInventoryItem[]) => void) {

    const newInventoryList: IBagInventoryItem[] = []
    let oldTag = 0
    oldInventoryList.forEach(item => {
      if (item.shortcut === '1') {
        oldTag = Number(item.tag)
        return
      }
      newInventoryList.push(item)
    })

    let newTag = oldTag
    switch (type) {
      case ItemType.Axe:
        newTag++;
        if (newTag < 101 || newTag > 107) {
          newTag = 101;
        }
        break
      case ItemType.Pickaxe:
        newTag++;
        if (newTag < 201 || newTag > 207) {
          newTag = 201;
        }
        break
      case ItemType.FishingRod:
        newTag++;
        if (newTag < 301 || newTag > 304) {
          newTag = 301;
        }
        break
    }
    this.getData(newTag, (data) => {
      const newItem: IBagInventoryItem = {
        userItemId: generateUUID(),
        itemId: generateUUID(),
        name: data?.name || '',
        description: '',
        icon: getCdnLink(data?.icon_url || ''),
        type: INVENTORY_TYPE_ENUM.equipment,
        maxDurability: 1,
        isNew: true,
        shortcut: '1',
        tag: String(newTag),
        quantity: 1,
        currentDurability: 1,
      }
      newInventoryList.push(newItem)
      cb(newInventoryList)
    })

  }
}