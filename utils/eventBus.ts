// 创建一个简单的事件总线系统，用于在非React环境中通知组件更新
type EventCallback = (data: any) => void;

interface EventMap {
  [event: string]: EventCallback[];
}

class EventBus {
  private events: EventMap = {};

  // 订阅事件
  subscribe(event: string, callback: EventCallback) {
    if (!this.events[event]) {
      this.events[event] = [];
    }
    this.events[event].push(callback);
    
    // 返回取消订阅函数
    return () => {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    };
  }

  // 发布事件
  publish(event: string, data?: any) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  }

  // 移除特定事件的所有订阅者
  clearEvent(event: string) {
    if (this.events[event]) {
      delete this.events[event];
    }
  }

  // 移除所有事件的所有订阅者
  clearAll() {
    this.events = {};
  }
}

// 导出单例
export const eventBus = new EventBus();

// 定义事件名称常量，方便使用
export const EventTypes = {
  TASK_LIST_UPDATED: 'taskListUpdated',
  TASK_PROGRESS_DATA: 'taskProgressData'
}; 