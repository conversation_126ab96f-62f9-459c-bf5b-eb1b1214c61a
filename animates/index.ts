import styled from "styled-components";
import { heartBeat, zoomInDown, fadeOutUp, shakeX, flash, shakeY } from "./keyFrames";

export const ComboAnimation = styled.div<{ duration?: string }>`
  animation-name: ${heartBeat};
  animation-duration: calc(${(props) => props.duration || "var(--animate-duration)"} * 1);
  animation-timing-function: ease-in-out;
`;

export const ZoomInDownAnimation = styled.div<{ duration?: string }>`
  animation-name: ${zoomInDown};
  animation-duration: calc(${(props) => props.duration || "var(--animate-duration)"} * 1);
  animation-timing-function: ease-in-out;
`;

export const FadeOutUpAnimation = styled.div<{ duration?: string }>`
  animation-name: ${fadeOutUp};
  animation-duration: calc(${(props) => props.duration || "var(--animate-duration)"} * 1);
  animation-timing-function: ease-in-out;
`;

export const ShakeXAnimation = styled.div<{ duration?: string }>`
  animation-name: ${shakeX};
  animation-duration: calc(${(props) => props.duration || "var(--animate-duration)"} * 1);
  animation-timing-function: ease-in-out;
`;

export const FlashAnimation = styled.div<{ duration?: string }>`
  animation-name: ${flash};
  animation-duration: calc(${(props) => props.duration || "var(--animate-duration)"} * 1);
  animation-timing-function: ease-in-out;
`;

export const ShakeYAnimation = styled.div<{ duration?: string }>`
  animation-name: ${shakeY};
  animation-duration: calc(${(props) => props.duration || "var(--animate-duration)"} * 1);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
`;

