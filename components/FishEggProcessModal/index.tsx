import React, {memo, UIEvent, useEffect, useMemo, useRef, useState,} from "react";
import styled from "styled-components";
import {motion} from "motion/react";
import Image from "next/image";
import CloseSvg from "/public/image/basic/close.png";
import RankModalBg from "/public/image/rank-modal-bg.png";
import fishReward from "/public/image/fishReward.png";
import Dialog from "@/commons/Dialog";
import styles from "@root/styles/AnimatedList.module.css";
import AnimatedItem
  from "@/components/EditAvatarPage/RecordingModal/AssistantQuestion/QuestionHistoryModal/components/AnimatedItem";
import Bottles from "./Bottles";
import {useDispatch, useSelector} from "react-redux";
import {IAppState} from "@/constant/type";
import useRewards from "../Rewards";

// .xyz 测试  .io 正式
const SUFFIX = ".io";

const ModalContent = styled(motion.div)`
  /* overflow-y: auto; */
  transform-origin: center bottom; /* 设置变换原点为底部中心，更符合弹跳效果 */
  /* border: 4px solid #ff8316; */
  background: #fff2e2;
  border-radius: 20px;
  box-sizing: border-box;
  position: relative;
  max-width: 800px;
  min-height: 400px;
  width: 100%;
  padding: 10px 20px;
  z-index: 100;
  .close-btn {
    position: absolute;
    /* width: 56px; */
    /* height: 56px; */
    cursor: pointer;
    top: -18px;
    right: 40px;
  }

  .history-title {
    position: absolute;
    left: 18%;
    top: -10%;
    transform: translate(-50%, 10%);
  }
`;

const ModalContentBackgroundImage = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(${RankModalBg.src}) no-repeat center center;
  background-size: cover;
  z-index: -1;
  border-radius: 20px;
`;

const ModalContentContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  height: 400px;
  gap: 2%;
  .left-container {
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 10;
    align-items: start;
    justify-content: center;
    flex-basis: 35%;
    .claim-btn {
      position: absolute;
      left: 50%;
      bottom: 0%;
      transform: translate(-50%, -40%);
      width: 80px;
      height: 40px;
      border-radius: 10px;
      background-color: #fc7922;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 0 #d65b0c; // 底部阴影创造立体感
      cursor: pointer;
    }
  }
  .right-container {
    flex-basis: 65%;
    padding: 20px 12px;
    border-radius: 20px;
  }
`;

const ItemContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  .dot {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #fc7922;
  }
  .item-title {
    width: 80%;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: start;
    border-radius: 10px;
    box-shadow: inset 0px 0px 8px rgba(0, 0, 0, 0.25);
    background-color: #e5dbc8;
    padding-left: 10px;
    // 字体间隔
    letter-spacing: 0.07em;
  }
  .item-status {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background-color: #fc7922;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 0 #d65b0c; // 底部阴影创造立体感
    cursor: pointer;

    &:hover {
      background-color: #ff8316;
    }

    // 点击按压效果
    &:active {
      transform: translateY(4px);
    }
  }
`;

// Replace the StyledDialog with styles for the modal container
const ModalWrapper = styled.div`
  position: relative;
`;

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  loading?: boolean;
  initialSelectedIndex?: number;
  enableArrowNavigation?: boolean;
  onItemSelect?: (item: string, index: number) => void;
  displayScrollbar?: boolean;
}
const FishEggProcessModal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  loading,
  initialSelectedIndex = -1,
  enableArrowNavigation = true,
  onItemSelect,
  displayScrollbar = true,
}) => {
  const listRef = useRef<HTMLDivElement>(null);
  const [selectedIndex, setSelectedIndex] =
    useState<number>(initialSelectedIndex);
  const [keyboardNav, setKeyboardNav] = useState<boolean>(false);
  const [topGradientOpacity, setTopGradientOpacity] = useState<number>(0);
  const [bottomGradientOpacity, setBottomGradientOpacity] = useState<number>(1);
  const { Rewards, setIsOpen } = useRewards();
  const dispatch = useDispatch();

  const { easterEggInfo, isFinished } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  const doMainList = useMemo(() => {
    if (easterEggInfo) {
      return easterEggInfo || [];
    }
    return [];
  }, [easterEggInfo]);


  const handleScroll = (e: UIEvent<HTMLDivElement>) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop, scrollHeight, clientHeight } = target;
    setTopGradientOpacity(Math.min(scrollTop / 50, 1));
    const bottomDistance = scrollHeight - (scrollTop + clientHeight);
    setBottomGradientOpacity(
      scrollHeight <= clientHeight ? 0 : Math.min(bottomDistance / 50, 1)
    );
  };

  // Keyboard navigation: arrow keys, tab, and enter selection
  useEffect(() => {
    if (!enableArrowNavigation) return;
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "ArrowDown" || (e.key === "Tab" && !e.shiftKey)) {
        e.preventDefault();
        setKeyboardNav(true);
        setSelectedIndex((prev) => Math.min(prev + 1, doMainList.length - 1));
      } else if (e.key === "ArrowUp" || (e.key === "Tab" && e.shiftKey)) {
        e.preventDefault();
        setKeyboardNav(true);
        setSelectedIndex((prev) => Math.max(prev - 1, 0));
      } else if (e.key === "Enter") {
        if (selectedIndex >= 0 && selectedIndex < doMainList.length) {
          e.preventDefault();
          if (onItemSelect) {
            onItemSelect(doMainList[selectedIndex].domainName, selectedIndex);
          }
        }
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [doMainList, selectedIndex, onItemSelect, enableArrowNavigation]);

  const isChargeLevel = useMemo(() => {
    const successCount = doMainList.reduce(
      (count, item) => count + (item.isSuccess ? 1 : 0),
      0
    );
    return Math.min(successCount * 25, 100);
  }, [doMainList]);

  // console.log("isChargeLevel=======", isChargeLevel);

  // Scroll the selected item into view if needed
  useEffect(() => {
    if (!keyboardNav || selectedIndex < 0 || !listRef.current) return;
    const container = listRef.current;
    const selectedItem = container.querySelector(
      `[data-index="${selectedIndex}"]`
    ) as HTMLElement | null;
    if (selectedItem) {
      const extraMargin = 50;
      const containerScrollTop = container.scrollTop;
      const containerHeight = container.clientHeight;
      const itemTop = selectedItem.offsetTop;
      const itemBottom = itemTop + selectedItem.offsetHeight;
      if (itemTop < containerScrollTop + extraMargin) {
        container.scrollTo({ top: itemTop - extraMargin, behavior: "smooth" });
      } else if (
        itemBottom >
        containerScrollTop + containerHeight - extraMargin
      ) {
        container.scrollTo({
          top: itemBottom - containerHeight + extraMargin,
          behavior: "smooth",
        });
      }
    }
    setKeyboardNav(false);
  }, [selectedIndex, keyboardNav]);

  const formatDomainName = (domainName: string, suffix?: string) => {
    // 监测domainName 是否存在 0.uniworlds || 1.uniworlds || 3.uniworlds
    if (domainName.startsWith("0.uniworlds")) {
      return `SatWorld(${domainName}${suffix || ""})`;
    } else if (domainName.startsWith("1.uniworlds")) {
      return `Fractal(${domainName}${suffix || ""})`;
    } else if (domainName.startsWith("3.uniworlds")) {
      return `DeTrading(${domainName}${suffix || ""})`;
    } else {
      return `${domainName}${suffix || ""}`;
    }
  };

  return (
    <>
      <Dialog isOpen={isOpen} onClose={onClose} width="800px" height="400px">
        <ModalWrapper>
          {isOpen && (
            <ModalContent onClick={(e) => e.stopPropagation()}>
              <ModalContentBackgroundImage />
              <Image
                src={fishReward.src}
                alt=""
                className="history-title"
                width={240}
                height={64}
              />
              <Image
                src={CloseSvg.src}
                alt=""
                onClick={onClose}
                className="close-btn"
                width={48}
                height={48}
              />

              <ModalContentContainer>
                <div className="left-container">
                  <Bottles chargeLevel={isChargeLevel} onClaim={() => {
                    setTimeout(() => {
                      setIsOpen(true);
                    }, 300);
                  }} />
                  {/* {isChargeLevel === 100 && isFinished && (
                    <div
                      className="claim-btn"
                      onClick={() => {
                        setTimeout(() => {
                          setIsOpen(true);
                        }, 300);
                      }}
                    >
                      Claim
                    </div>
                  )} */}
                </div>
                <div className="right-container">
                  <div
                    ref={listRef}
                    className={`${styles.scrollList} ${
                      !displayScrollbar ? styles.noScrollbar : ""
                    }`}
                    onScroll={handleScroll}
                  >
                    {doMainList.map((item, index) => (
                      <AnimatedItem
                        key={index}
                        title={item.domainName + index}
                        id={item.domainName + index}
                        index={index}
                        marginBottom="0.6rem"
                      >
                        <ItemContainer>
                          <div
                            className="dot"
                            style={{
                              backgroundColor: item.isSuccess
                                ? "rgb(134, 134, 134)"
                                : "#FC7922",
                            }}
                          />

                          <div className="item-title">
                            {formatDomainName(item.domainName, SUFFIX)}
                          </div>
                          {/* 点击该按钮 将通过item.title当前网站重定向地址 */}
                          <div
                            className="item-status"
                            onClick={() => {
                              // 这里我需要拼接端口域名吗？比如我在localhost:3000 或者 本机127.0.0.1:3000 访问以及测试服和正式服，情况又是怎样的呢
                              window.location.href = `https://${item.domainName}${SUFFIX}`;

                            }}
                            style={{
                              cursor: item.isSuccess
                                ? "not-allowed"
                                : "pointer",
                              filter: item.isSuccess
                                ? "grayscale(100%)"
                                : "none",
                            }}
                          >
                            Go
                          </div>
                        </ItemContainer>
                      </AnimatedItem>
                    ))}
                  </div>
                </div>
              </ModalContentContainer>
            </ModalContent>
          )}
        </ModalWrapper>
      </Dialog>
      <Rewards />
    </>
  );
};

export default memo(FishEggProcessModal);
