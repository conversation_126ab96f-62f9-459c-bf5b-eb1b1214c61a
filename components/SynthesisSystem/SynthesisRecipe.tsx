import React, { useState, useEffect, useMemo, useRef } from "react";
import styled from "styled-components";
import StaggeredAnimation from "@/commons/StaggeredAnimation";
import LocalLoading from "@/components/LoadingContent";

const SynthesisRecipeContainer = styled.div`
  height: 500px;
  background: #fef1df;
  border-radius: 32px;
  border: 2px solid #000;
  box-shadow: inset 0 0 0 6px #ff9f1c;
  padding: 30px;
  box-sizing: border-box;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 320px;
`;

// const RecipeTitle = styled.h3`
//   font-family: JetBrainsMono, monospace;
//   font-size: 18px;
//   font-weight: 700;
//   text-align: center;
//   margin: 0 0 20px;
//   color: #ff8316;
//   text-shadow: 1px 1px 0px rgba(0, 0, 0, 0.2);
// `;

const RecipeList = styled.div`
  flex: 1 1 0;
  overflow-y: auto;
  display: grid;
  grid-template-columns: repeat(2, 110px);
  grid-auto-rows: 106px;
  gap: 16px;
  justify-content: center;
`;

const RecipeCard = styled.div<{ isSelected: boolean; active?: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f9f2e2;
  border-radius: 16px;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
  border: 4px solid
    ${(props) =>
      props.isSelected
        ? "#FF8316"
        : props.active === false
        ? "transparent"
        : "rgba(194, 184, 162, 0.3)"}; // c2b8a2 调整透明度
  cursor: pointer;
  position: relative;
  /* padding: 10px; */
  transition: transform 0.2s, border-color 0.2s;
  &::after {
    content: "";
    display: ${(props) => (props.active === false ? "block" : "none")};
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.45);
    border-radius: 13px;
    pointer-events: none;
    z-index: 2;
  }
`;

const ItemImage = styled.div<{ bgImage: string }>`
  width: 104px;
  height: 108px;
  background-image: ${(props) => `url(${props.bgImage})`};
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
`;

const RecipeCardContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

// const RecipeName = styled.span`
//   font-size: 12px;
//   margin-top: 8px;
//   text-align: center;
//   max-width: 90px;
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
// `;

// const AvailabilityIndicator = styled.div<{ available: boolean }>`
//   width: 12px;
//   height: 12px;
//   position: absolute;
//   top: 5px;
//   right: 5px;
//   border-radius: 50%;
//   background-color: ${(props) => (props.available ? "#4CAF50" : "#FF5722")};
//   border: 1px solid #fff;
// `;

// 修改接口定义，使用synthesisTag替代itemId
interface ISynthesisRecipe {
  synthesisTag: string; // 改为使用synthesisTag
  itemTag: string; // 添加itemTag字段
  itemName?: string; // 改为使用itemName
  itemIcon?: string; // 改为使用itemIcon
  active?: boolean;
}

interface ISynthesisRecipeProps {
  recipeList: ISynthesisRecipe[];
  onSelectRecipe?: (recipe: ISynthesisRecipe) => void;
  selectedRecipeId?: string; // 现在这个是synthesisTag
  loading?: boolean;
}

const SynthesisRecipe = ({
  recipeList,
  onSelectRecipe,
  selectedRecipeId,
  loading,
}: ISynthesisRecipeProps) => {
  
  const safeRecipeList = useMemo(() => {
    return Array.isArray(recipeList) ? recipeList : [];
  }, [recipeList]);

  // 对配方列表进行排序，active为true的排在前面
  const sortedRecipeList = useMemo(() => {
    return [...safeRecipeList].sort((a, b) => {
      // 边界条件：处理undefined或null的active值
      const aActive = a.active === true;
      const bActive = b.active === true;

      if (aActive && !bActive) return -1;
      if (!aActive && bActive) return 1;
      return 0;
    });
  }, [safeRecipeList]);

  // 使用ref来追踪之前的recipeList
  const prevRecipeListRef = useRef<ISynthesisRecipe[]>([]);

  // 检测列表是否已完全改变（模态框重新打开）
  const isListReset = useMemo(() => {
    if (prevRecipeListRef.current.length === 0 && recipeList.length > 0) {
      return true;
    }

    if (prevRecipeListRef.current.length !== recipeList.length) {
      return true;
    }

    // 检查第一个元素是否改变
    if (prevRecipeListRef.current.length > 0 && recipeList.length > 0) {
      return (
        prevRecipeListRef.current[0].synthesisTag !== recipeList[0].synthesisTag
      );
    }

    return false;
  }, [recipeList, prevRecipeListRef.current]);

  // 更新ref
  useEffect(() => {
    prevRecipeListRef.current = recipeList;
  }, [recipeList]);

  const [selectedId, setSelectedId] = useState<string | undefined>(
    selectedRecipeId || sortedRecipeList[0]?.synthesisTag
  );

  // 当列表重置时，重新选择第一个元素
  useEffect(() => {
    if (isListReset && sortedRecipeList.length > 0) {
      const firstId = sortedRecipeList[0].synthesisTag;
      setSelectedId(firstId);
      if (onSelectRecipe) {
        onSelectRecipe(sortedRecipeList[0]);
      }
    }
  }, [isListReset, sortedRecipeList, onSelectRecipe]);

  // 当外部selectedRecipeId变化时，更新内部状态
  useEffect(() => {
    if (selectedRecipeId && selectedRecipeId !== selectedId) {
      setSelectedId(selectedRecipeId);
    }
  }, [selectedRecipeId, selectedId]);

  // 当排序后的列表变化时，如果没有选择项，则选择第一个
  useEffect(() => {
    if (!selectedId && sortedRecipeList.length > 0) {
      const firstId = sortedRecipeList[0].synthesisTag;
      setSelectedId(firstId);
      if (onSelectRecipe) {
        onSelectRecipe(sortedRecipeList[0]);
      }
    }
  }, [sortedRecipeList, selectedId, onSelectRecipe]);

  const handleSelectRecipe = (recipe: ISynthesisRecipe) => {
    setSelectedId(recipe.synthesisTag);
    if (onSelectRecipe) {
      onSelectRecipe(recipe);
    }
  };

  return (
    <SynthesisRecipeContainer>
      {/* <RecipeTitle>Synthesis Recipes</RecipeTitle> */}

      {loading ? (
        <div
          style={{
            textAlign: "center",
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <LocalLoading />
        </div>
      ) : sortedRecipeList.length > 0 ? (
        <RecipeList>
          {sortedRecipeList.map((recipe, index) => (
            <StaggeredAnimation
              index={index}
              initialScale={0.7}
              duration={0.5}
              bounceEffect={true}
              key={recipe.synthesisTag} // 使用synthesisTag作为key
              staggerDelay={0.1}
            >
              <RecipeCard
                key={recipe.synthesisTag} // 使用synthesisTag作为key
                isSelected={recipe.synthesisTag === selectedId} // 对比synthesisTag
                onClick={() => handleSelectRecipe(recipe)}
                active={recipe.active}
              >
                <RecipeCardContent>
                  <ItemImage
                    bgImage={recipe.itemIcon || ""} // 使用itemIcon
                  />
                  {/* <RecipeName>{recipe.itemName}</RecipeName> */}
                </RecipeCardContent>
                {/* <AvailabilityIndicator available={recipe.active !== false} /> */}
              </RecipeCard>
            </StaggeredAnimation>
          ))}
        </RecipeList>
      ) : (
        <div
          style={{
            textAlign: "center",
            width: "100%",
            height: "100%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          No available formulas
        </div>
      )}
    </SynthesisRecipeContainer>
  );
};

export default SynthesisRecipe;
