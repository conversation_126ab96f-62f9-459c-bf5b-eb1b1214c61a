import styled from "styled-components";

export const MyOrdersPageView = styled.div`
  width: 100%;
  height: 100%;
  background-color: #d7d7d7;
  padding: 0 20px 100px 20px;
  overflow-y: auto;
  box-sizing: border-box;
  
  .my-order-page{
    max-width: 1200px;
    margin: 0 auto;
    &>h1{
      font-family: JetBrainsMonoBold;
      font-size: 38px;
      font-weight: 700;
      line-height: 45.99px;
      text-align: left;
      color: #140F08;
    }
    .my-order-card{
      margin-top: 40px;
      padding: 40px;
      box-sizing: border-box;
      border-radius: 32px;
      border: 1px solid #9A9A9A;
      background-color: #FFFFFF;
      .order-list{
        margin-top: 32px;
        .order-item-title{
          display: grid;
          grid-template-columns: 380px 1fr 1fr 1fr;
          border-bottom: 1px solid #828282;
          &>div{
            font-family: JetBrainsMono;
            font-size: 18px;
            font-weight: 400;
            line-height: 21.78px;
            padding: 13px 10px;
            box-sizing: border-box;
            color: #828282;
            word-break: break-all;

            &:nth-last-child(1){
              text-align: right;
            }
          }
        }
        .order-item{
          display: grid;
          grid-template-columns: 380px 1fr 1fr 1fr;
          padding: 24px 0;
          border-bottom: 1px solid #C2C2C2;
          &:nth-last-child(1){
            border: 0;
          }
          &>div{
            font-family: JetBrainsMono;
            font-size: 18px;
            font-weight: 400;
            line-height: 21.78px;
            padding: 13px 10px;
            box-sizing: border-box;
            color: #140F08;
            display: flex;
            align-items: center;
            word-break: break-all;
            &:nth-last-child(1){
              justify-content: end;
            }
            &.item-hash{
              display: flex;
              align-items: center;
              gap: 16px;
              &>img{
                width: 32px;
                height: 32px;
                cursor: pointer;
              }
            }
          }
        }
      }
      .no-data, .loading-view{
        padding: 100px 0;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        &>img{
          width: 200px;
          height: 200px;
        }
        &>p{
          font-family: JetBrainsMono;
          font-size: 20px;
          font-weight: 400;
          line-height: 24.2px;
          text-align: center;
          margin: 16px 0 0 0;
        }
      }
    }
  }
  
`

export const FilterView = styled.div`
  display: flex;
  align-items: center;
  gap: 24px;
  .search-view{
    flex: 1;
    height: 48px;
    border: 1px solid #9A9A9A;
    border-radius: 16px;
    display: flex;
    align-items: center;
    padding: 12px 16px;
    box-sizing: border-box;
    &>img{
      width: 24px;
      height: 24px;
    }
    &>input{
      padding: 0 0 0 12px;
      box-sizing: border-box;
      outline: none;
      background-color: transparent;
      font-family: JetBrainsMono;
      font-size: 18px;
      font-weight: 400;
      line-height: 21.78px;
      text-align: left;
      color: #000000;
      border: 0;
      flex: 1;
      &::placeholder{
        color: #828282;
      }
    }
  }
`

export const SelectStatusView = styled.div`
  width: 168px;
  height: 48px;
  border: 1px solid #FF8316;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  cursor: pointer;
  &>span{
    font-family: JetBrainsMono;
    font-size: 18px;
    font-weight: 400;
    line-height: 21.78px;
    text-align: left;
    color: #FF8316;
  }
  &>img {
    width: 24px;
    height: 24px;
  }
  position: relative;
  .status-list-view{
    width: calc(100% + 2px);
    position: absolute;
    left: -1px;
    top: 100%;
    background: #ffffff;
    padding: 0 36px;
    box-sizing: border-box;
    max-height: 0;
    overflow: hidden;
    border-bottom-right-radius: 16px;
    border-bottom-left-radius: 16px;

    .line{
      width: 100%;
      height: 1px;
      border-bottom: 1px solid #615A57;
    }
    .status-list{
      box-sizing: border-box;
      .status-item{
        height: 64px;
        display: flex;
        align-items: center;
        justify-content: center;
        &>span{
          font-family: JetBrainsMono;
          font-size: 18px;
          font-weight: 400;
          line-height: 21.78px;
          text-align: center;
          color: #615A57;
          text-decoration: none;
          padding: 10px 0;
        }
        &:hover{
          &>span{
            border-bottom: 1px solid #615A57;
          }
        }
      }
    }
  }
  &:hover{
      border: 1px solid #FF8316;
      border-bottom-right-radius: 0;
      border-bottom-left-radius: 0;
      border-bottom: 0;

      .status-list-view{
        max-height: 600px;
        border: 1px solid #FF8316;
        border-top: 0;
        overflow: hidden;
        z-index: 10;
        transform: translateZ(0);
      }
  }
`
