import styled from "styled-components";

const RankItemHeight = {
  first: 110,
  second: 100,
  third: 100,
  fourth: 100,
  fifth: 100,
};

export const CommunityContainer = styled.div`
  width: 100%;
  height: 440px;
  display: flex;
  flex-direction: column;
  align-items: center;
  overflow: hidden;
  .top {
    position: relative;
    width: 55%;
    margin: 0 auto;
    height: 82px;
    .community-title {
      position: absolute;
      z-index: -100;
      width: 400px;
      height: 100px;
      left: 50%;
      top: -20px;
      transform: translate(-50%, 0);
    }
    .clock-container {
      position: absolute;
      right: 35px;
      bottom: 20%;
      transform: translate(100%, 0);
      z-index: 10;
      background: #ffffff;
      border-radius: 20px;
      display: flex;
      align-items: center;
      height: 30px;
      padding-left: 20px;
      padding-right: 15px;
      box-shadow: 4px 4px 4px 0px #ffffff inset, 0px 4px 0px 0px #d9c4a3,
        2px 0px 0px 0px #d9c4a3, 0px 6px 12px -2px rgba(0, 0, 0, 0.15);
      .clock-image {
        position: absolute;
        left: -8px;
        top: -10px;
        z-index: 20;
      }
    }
  }
  .bottom {
    flex: 1;
    min-height: 0;
    overflow-y: auto;
    max-height: 350px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    width: 100%;
    /* 新增：滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #d9c4a3 #f5f5f5;
  }
  .bottom::-webkit-scrollbar {
    width: 8px;
    border-radius: 8px;
    background: #f5f5f5;
  }
  .bottom::-webkit-scrollbar-thumb {
    background: #d9c4a3;
    border-radius: 8px;
  }
  .bottom-content {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  /* 统一排名基础类 */
  .rank-item {
    position: relative;
    width: calc(100% - 5px);
    overflow: hidden;
    border-radius: 20px;
    cursor: pointer;

    /* 排名特定高度和样式 */
    &[data-rank='1'] {
      height: 110px;
      border: 3px solid #ffcd04;
      background: #ffcd04;
    }
    &[data-rank='2'] {
      height: 100px;
      border: 3px solid #a3a9bc;
      background: #a3a9bc;
    }
    &[data-rank='3'] {
      height: 100px;
      border: 3px solid #cf8952;
      background: #cf8952;
    }
    &[data-rank='4'],
    &[data-rank='5'] {
      height: 100px;
      border: 3px solid #14110a;
      background: #14110a;
    }

    /* 排名图标样式 */
    .rank-image {
      position: absolute;
      
      &[data-rank='1'] {
        left: -10px;
        top: -5%;
        z-index: 100;

        .first-rank-image {
          /* 特殊样式给第一名图标 */
        }
      }
      
      &[data-rank='2'], 
      &[data-rank='3'] {
        left: 15px;
        top: 0%;
        z-index: 100;
        width: 50px;
        height: 64px;
        background-size: cover;
      }

      &[data-rank='2'] {
        background: url("/image/2-1.png") no-repeat center center;
        background-size: cover;
      }

      &[data-rank='3'] {
        background: url("/image/3-1.png") no-repeat center center;
        background-size: cover;
      }

      &[data-rank='4'],
      &[data-rank='5'] {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: #fca346;
        position: absolute;
        z-index: 20;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 30px;
        left: 15px;
        top: 6px;
      }
    }

    /* 排名信息样式 */
    .rank-info {
      position: absolute;
      display: flex;
      align-items: center;
      z-index: 90;
      
      &[data-rank='1'] {
        left: 50px;
        top: 25px;
        gap: 12px;
      }
      
      &[data-rank='2'],
      &[data-rank='3'],
      &[data-rank='4'],
      &[data-rank='5'] {
        left: 55px;
        top: 15px;
        gap: 16px;
        z-index: 10;
      }
    }

    .rank-info-image {
      border-radius: 50%;
      overflow: hidden;
      border: 3px solid #fff;
      
      &[data-rank='1'] {
        width: 70px;
        height: 70px;
      }
      
      &[data-rank='2'],
      &[data-rank='3'],
      &[data-rank='4'],
      &[data-rank='5'] {
        width: 60px;
        height: 60px;
      }
    }

    .rank-info-text {
      display: flex;
      flex-direction: column;
      
      .potato-count {
        font-size: 16px;
        color: #fff;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 80px;
        text-shadow: 0 0 1px #fff;
        -webkit-text-stroke: 1px #fff;
      }
    }

    .rank-info-text[data-rank='1'] .potato-count {
      background: #99601a;
      border-radius: 12px;
      height: 32px;
      padding: 0 10px;
    }

    .rank-info-text[data-rank='2'] .potato-count {
      background: #354a5c;
      height: 28px;
      padding: 0 5px;
    }

    .rank-info-text[data-rank='3'] .potato-count {
      background: #80430f;
      height: 28px;
      padding: 0 5px;
    }

    .rank-info-text[data-rank='4'] .potato-count,
    .rank-info-text[data-rank='5'] .potato-count {
      background: #80430f;
      height: 28px;
      padding: 0 5px;
    }

    /* 默认after样式，设置渐变背景 */
    &::after {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      backdrop-filter: blur(1px);
      z-index: 1;
    }

    /* 各社区特定样式 */
    &.community-wangcai {
      border-color: #ffcd04;
      background: #ffcd04;
      &::before {
        content: "";
        position: absolute;
        left: -10px;
        top: 0;
        width: 105%;
        height: 101%;
        background: url("/image/1.png") no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(1px);
      }
      &::after {
        background: linear-gradient(
          90deg,
          rgba(255, 209, 42, 1) 0%,
          rgba(255, 178, 23, 0) 100%
        );
      }
    }
    &.community-potato {
      border-color: #a3a9bc;
      background: #a3a9bc;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 103%;
        height: 100%;
        background: url("/image/2.png") no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(1px);
      }
      &::after {
        background: linear-gradient(
          90deg,
          rgba(178, 183, 201, 1) 0%,
          rgba(142, 147, 166, 0) 100%
        );
      }
    }
    &.community-thelonelybit {
      border-color: #cf8952;
      background: #cf8952;
      &::before {
        content: "";
        position: absolute;
        left: 0;
        top: 0;
        width: 103%;
        height: 100%;
        background: url("/image/3.png") no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(1px);
      }
      &::after {
        background: linear-gradient(
          90deg,
          rgba(224, 125, 57, 1) 0%,
          rgba(217, 168, 128, 0) 100%
        );
      }
    }
    &.community-pizza {
      border-color: #14110a;
      background: #14110a;
      &::before {
        content: "";
        position: absolute;
        left: -25px;
        top: 0;
        width: 106%;
        height: 100%;
        background: url("/image/4.png") no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(1px);
      }
      &::after {
        background-color: rgba(0, 0, 0, 0.5);
      }
      .potato-count {
        background: #80430f;
      }
    }
    &.community-domoducks {
      border-color: #14110a;
      background: #14110a;
      &::before {
        content: "";
        position: absolute;
        left: -25px;
        top: 0;
        width: 106%;
        height: 100%;
        background: url("/image/5.png") no-repeat center center;
        background-size: cover;
        z-index: 0;
        filter: blur(1px);
      }
      &::after {
        background-color: rgba(0, 0, 0, 0.5);
      }
      .potato-count {
        background: #80430f;
      }
    }
  }
`;

export { RankItemHeight }; 