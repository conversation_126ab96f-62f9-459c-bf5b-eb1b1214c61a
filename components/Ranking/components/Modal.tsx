import React, {memo, useEffect, useMemo, useRef} from "react";
import styled from "styled-components";
import {motion} from "motion/react";
import Image from "next/image";
import CloseSvg from "/public/image/basic/close.png";
import RankModalBg from "/public/image/rank-modal-bg.png";
import AxeCell from "/public/image/axecell.png";
import Boom from "/public/image/boom.png";
import RodMasters from "/public/image/RodMasters.png";
import RankImg from "/public/image/rankimg.png";
import Mu from "/public/image/mu.png";
import s1 from "/public/image/s1.png";
import ClockImage from "/public/image/time.png";
import CountDownClock from "./CountDownClock";
import {useSelector} from "react-redux";
import {IAppState} from "@/constant/type";
import LocalLoading from "@/components/LoadingContent";
import Dialog from "@/commons/Dialog";
import Menu, {MenuType} from "./Menu";
import S3 from "/public/image/s3.png";
import Tabs, {TabItem, TabsRef} from "./Tabs";
import Community from "./Community";

const ModalContent = styled(motion.div)`
  /* overflow-y: auto; */
  transform-origin: center bottom; /* 设置变换原点为底部中心，更符合弹跳效果 */
  /* border: 4px solid #ff8316; */
  background: #fff2e2;
  border-radius: 20px;
  box-sizing: border-box;
  position: relative;
  max-width: 800px;
  min-height: 460px;
  width: 100%;
  padding: 10px 20px;
  z-index: 100;
  .close-btn {
    position: absolute;
    /* width: 56px; */
    /* height: 56px; */
    cursor: pointer;
    top: -18px;
    right: 40px;
  }

  .history-title {
    position: absolute;
    left: 18%;
    top: -10%;
    transform: translate(-50%, 10%);
  }
`;

const ModalContentBackgroundImage = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(${RankModalBg.src}) no-repeat center center;
  background-size: cover;
  z-index: -1;
  border-radius: 20px;
`;

const ModalContentContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  height: 460px;
  gap: 2%;
  .left-container {
    display: flex;
    position: relative;
    z-index: 10;
    align-items: start;
    justify-content: center;
    padding-top: 5%;
    flex-basis: 25%;
    .axe-cell-image {
      /* padding-top: 10px; */
    }
    .clock-container {
      position: absolute;
      left: 50%;
      top: 35%;
      transform: translate(-50%, -50%);
      z-index: 10;
      background: #ffffff;
      border-radius: 20px;
      display: flex;
      padding: 0 20px;
      align-items: center;
      height: 30px;
      box-shadow: 4px 4px 4px 0px #ffffff inset, 0px 4px 0px 0px #d9c4a3,
        2px 0px 0px 0px #d9c4a3, 0px 6px 12px -2px rgba(0, 0, 0, 0.15);
      .clock-image {
        position: absolute;
        left: -8px;
        top: -10px;
        z-index: 20;
      }
    }
    .clock-end-text {
      position: absolute;
      left: 50%;
      top: 35%;
      transform: translate(-50%, -50%);
      z-index: 10;
      font-size: 16px;
      font-weight: 700;
      width: 100%;
      text-align: center;
      background: #ffffff;
      border-radius: 20px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .mu-image {
      position: absolute;
      left: 50%;
      top: 57%;
      transform: translate(-50%, -50%);
      z-index: -1;
    }
  }
  .right-container {
    background-color: #fee2ab;
    padding: 12px;
    border-radius: 20px;
    border: 8px solid #ff8316;
  }
`;

// Replace the StyledDialog with styles for the modal container
const ModalWrapper = styled.div`
  position: relative;
  height: 100%;
`;

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  loading?: boolean;
  endTimestamp?: number;
  tabItems?: TabItem[];
  currentChangeTab?: (tab: TabItem) => void;
  currentMenu?: string;
  onMenuClick?: (menu: MenuType) => void;
  initialActiveIndex?: number;
  communityRankList?: any[];
  communityEndTimestamp?: number;
}

// 定义活动主题配置映射
const ACTIVITY_CONFIG = {
  "menu-1": {
    title: AxeCell.src,
    image: Mu.src,
    dimensions: { width: 280, height: 360 },
  },
  "menu-2": {
    title: Boom.src,
    image: s1.src,
    dimensions: { width: 236, height: 236 },
  },
  "menu-3": {
    title: RodMasters.src,
    image: S3.src,
    dimensions: { width: 266, height: 300 },
  },
};

const RankingModal: React.FC<ModalProps> = ({
  isOpen,
  onClose,
  children,
  loading,
  endTimestamp,
  tabItems,
  currentChangeTab,
  currentMenu,
  onMenuClick,
  initialActiveIndex,
  communityRankList,
  communityEndTimestamp,
}) => {
  const tabsRef = useRef<TabsRef | null>(null);

  const { menuType } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );

  // 使用配置映射获取活动相关信息
  const activityConfig = useMemo(
    () =>
      ACTIVITY_CONFIG[menuType as keyof typeof ACTIVITY_CONFIG] || {
        title: "",
        image: "",
        dimensions: { width: 0, height: 0 },
      },
    [menuType]
  );

  const handleTabChange = (index: number, tab: TabItem) => {
    currentChangeTab && currentChangeTab(tab);
  };

  useEffect(() => {
    if (initialActiveIndex !== undefined) {
      tabsRef.current?.setActiveTab(initialActiveIndex);
    }
  }, [initialActiveIndex, currentMenu]);

  return (
    <Dialog isOpen={isOpen} onClose={onClose} width="800px" height="460px">
      <ModalWrapper>
        <Menu
          onMenuClick={(menu) => {
            onMenuClick && onMenuClick(menu);
          }}
          currentMenu={currentMenu}
        />

        {isOpen && (
          <ModalContent onClick={(e) => e.stopPropagation()}>
            <ModalContentBackgroundImage />
            <Image
              src={RankImg.src}
              alt=""
              className="history-title"
              width={240}
              height={64}
            />
            <Image
              src={CloseSvg.src}
              alt=""
              onClick={onClose}
              className="close-btn"
              width={48}
              height={48}
            />
            {currentMenu !== "menu-0" ? (
              <ModalContentContainer>
                <div className="left-container">
                  {activityConfig.title && (
                    <Image
                      src={activityConfig.title}
                      alt=""
                      width={230}
                      height={78}
                      className="axe-cell-image"
                    />
                  )}
                  {/* 阶段性倒计时 */}
                  <div className="clock-container">
                    <Image
                      src={ClockImage}
                      alt="Timer"
                      priority
                      className="clock-image"
                      width={48}
                      height={48}
                    />
                    <CountDownClock
                      endTimestamp={endTimestamp || 0}
                      onCountdownEnd={() => {}}
                    />
                  </div>
                  {activityConfig.image && (
                    <Image
                      src={activityConfig.image}
                      alt=""
                      width={activityConfig.dimensions.width}
                      height={activityConfig.dimensions.height}
                      className="mu-image"
                    />
                  )}
                </div>
                <div
                  style={{
                    display: "flex",
                    flexDirection: "column",
                    width: "75%",
                    height: "100%",
                    marginTop: "20px",
                  }}
                >
                  <Tabs
                    tabs={tabItems as TabItem[]}
                    onTabChange={handleTabChange}
                    ref={tabsRef}
                  />
                  <div
                    className="right-container"
                    style={{
                      height: loading
                        ? "calc(100% - 100px)"
                        : "calc(100% - 100px)",
                    }}
                  >
                    {loading ? <LocalLoading /> : children}
                  </div>
                </div>
              </ModalContentContainer>
            ) : (
              <Community
                endTimestamp={communityEndTimestamp || 0}
                loading={loading}
                communityRankList={communityRankList}
                onClose={onClose}
              />
            )}
          </ModalContent>
        )}
      </ModalWrapper>
    </Dialog>
  );
};

export default memo(RankingModal);
