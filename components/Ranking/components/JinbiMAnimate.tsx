import { motion } from "motion/react";
import React from "react";
import styled from "styled-components";

// 容器组件，同时包含金币和字母M
const AnimationContainer = styled.div`
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  /* height: 120px; // 确保有足够高度容纳动画 */
  position: absolute;
  top: 0;
  right: 40px;
  z-index: 300;
  .jinbi1 {
    position: absolute;
    right: -4px;
    top: -7px;
    z-index: 4;
  }
`;

// M字母的样式
const MLetter = styled(motion.div)`
  color: #fff;
  font-size: 24px; /* 更大的字体 */
  font-weight: 900; /* 更粗的字体 */
  color: #ffffff; /* 纯白色 */
  /* 多层文字阴影创建粗描边效果 */
  text-shadow: 
      /* 轻微的全方位描边 */ -1px -1px 0 rgba(0, 0, 0, 0.4),
    1px -1px 0 rgba(0, 0, 0, 0.4), -1px 1px 0 rgba(0, 0, 0, 0.4),
    1px 1px 0 rgba(0, 0, 0, 0.4),
    /* 底部明显的阴影 */ 0 2px 0 rgba(0, 0, 0, 0.7),
    0 3px 4px rgba(0, 0, 0, 0.5);
  background: linear-gradient(to bottom, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  background-clip: text;
`;

// 金币和M字母的组合动画
const JinBiMAnimate = ({ children }: { children: React.ReactNode }) => {
  // 同步的动画控制
  const animationDuration = 1.8;

  return (
    <AnimationContainer>
      {/* 金币部分 */}
      <motion.div
        animate={{
          y: [0, -5, -30, -5, -3], // 添加初始和结束阶段的小移动
          rotateY: [0, 90, 360, 720],
          scale: [1, 1, 1.1, 1, 1],
        }}
        className="jinbi1"
        transition={{
          duration: animationDuration,
          times: [0, 0.15, 0.5, 0.85, 1], // 更精细的控制
          ease: "easeInOut",
          repeat: Infinity,
          repeatType: "loop",
        }}
      >
        {children}
      </motion.div>

      {/* M字母部分 */}
      <MLetter
        animate={{
          // 简化为4个关键帧，去掉中间的停留点
          y: [0, 16, -2, -3], // 压缩下去，弹起，然后直接下降
          scale: [1, 1.2, 0.95, 1],
          opacity: [0.9, 1, 0.95, 0.9],
        }}
        transition={{
          duration: animationDuration,
          // 关键调整：减少字母到达顶点后的停留时间
          times: [0, 0.15, 0.2, 1], // 到达顶点后立即开始下降
        // times: [0, 0.15, 0.2, 0.85], 
          ease: "easeInOut", // 保持平滑的过渡
          repeat: Infinity,
          repeatType: "loop",
        }}
      >
        m
      </MLetter>
    </AnimationContainer>
  );
};

export default JinBiMAnimate;
