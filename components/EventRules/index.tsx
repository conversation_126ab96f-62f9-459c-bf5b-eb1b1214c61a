import { useEffect, useState } from "react";
import Dialog from "@/commons/Dialog";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

function EventRules({ isOpen, onClose, title, children }: ModalProps) {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    return () => setIsMounted(false);
  }, []);

  if (!isMounted) {
    return null;
  }

  return (
    <Dialog isOpen={isOpen} onClose={onClose}>
      {children}
    </Dialog>
  );
}

export default EventRules;
