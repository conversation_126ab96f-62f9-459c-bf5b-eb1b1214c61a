import styled from "styled-components";
import Image from "next/image";
import Event1 from "/public/image/e1.png";

import StartButton from "./StartButton";
import { motion } from "framer-motion";
import { useEffect, useState } from "react";

const RulesContentContainer = styled.div`
  width: 850px;
  height: 530px;
  background-image: url(${Event1.src});
  background-size: 100% 100%;
  background-position: center;
  background-repeat: no-repeat;
  .event-image {
    z-index: 10;
    position: relative;
    top: -10%;
    display: block;
    margin: 0 auto;
  }
  .event-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
    position: relative;
    .tep2 {
      position: relative;
      top: -40px;
    }
  }
`;

// 定义容器的变体
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.5, // 子元素之间的延迟时间
      delayChildren: 0.3, // 第一个子元素开始前的延迟
    },
  },
};

// 定义子元素的变体
const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.8,
      ease: "easeOut",
    },
  },
};

const popupImages = [
  {
    src: "/image/tep1.png",
    imgObj: "",
    alt: "Popup image 1",
    width: 260,
    height: 340,
    className: "",
  },
  {
    src: "/image/tep2.png",
    imgObj: "",
    alt: "Popup image 2",
    width: 260,
    height: 340,
    className: "tep2",
  },
  {
    src: "/image/tep3.png",
    imgObj: "tep3",
    alt: "Popup image 3",
    width: 260,
    height: 340,
    className: "",
  },
];

const event1 = {
  src: "/image/community.png",
  alt: "event2",
  width: 400,
  height: 100,
  className: "",
};

function RulesContent({ onClose }: { onClose: () => void }) {
  const [imagesPreloaded, setImagesPreloaded] = useState(false);

  useEffect(() => {
    if (!imagesPreloaded && typeof window !== "undefined") {
      let loadedCount = 0;
      const totalImages = popupImages.length;

      popupImages.forEach((img) => {
        const imgElement = document.createElement("img");
        imgElement.src = img.src;

        // 监听加载完成事件
        imgElement.onload = () => {
          loadedCount++;
          if (loadedCount === totalImages) {
            setImagesPreloaded(true);
          }
        };
      });
    }
  }, [imagesPreloaded]);

  return (
    <RulesContentContainer>
      <Image
        className="event-image"
        src={event1.src}
        alt={event1.alt}
        width={event1.width}
        height={event1.height}
      />
      <motion.div
        className="event-image-container"
        variants={containerVariants}
        initial="hidden"
        animate={imagesPreloaded ? "visible" : "hidden"}
      >
        {popupImages.map((img) => (
          <motion.div
            key={img.alt}
            variants={itemVariants}
            className={img.className}
          >
            <Image
              src={img.src}
              alt={img.alt}
              width={img.width}
              height={img.height}
            />
          </motion.div>
        ))}
      </motion.div>
      <StartButton text="Start!" onClick={onClose} />
    </RulesContentContainer>
  );
}

export default RulesContent;
