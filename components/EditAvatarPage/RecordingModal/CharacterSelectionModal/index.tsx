import {CharacterSelectionModalView} from "./style";
import Modal from "../../BasicComponents/Modal";
import RoleImg from '/public/image/character/role.png'
import PotatoImg from '/public/image/character/potato.png'
import {useDispatch, useSelector} from "react-redux";
import {CHARACTER_ENUM, IAppState, STORAGE_MENU_ENUM} from "../../../../constant/type";
import {setStorageMenu, setUsedCharacter} from "../../../../store/app";
import {IBasicSummaryData} from "../../../../constant/type";
import classNames from "classnames";
import {useEffect, useMemo, useState} from "react";
import GlobalSpaceEvent, {GlobalDataKey, SpaceStatus} from "../../../../world/Global/GlobalSpaceEvent";

export default function CharacterSelectionModal({visible, onClose, basicSummaryData}: {
  visible: boolean,
  onClose: () => void,
  basicSummaryData: IBasicSummaryData|null
}){
  const {storageMenu, usedCharacter} = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const [thisSelect, setThisSelect] = useState<CHARACTER_ENUM>(CHARACTER_ENUM.character)
  const dispatch = useDispatch();
  useMemo(() => {
    setThisSelect(usedCharacter)
  }, [usedCharacter])
  const onSelect = () => {
    GlobalSpaceEvent.SetDataValue<string>(GlobalDataKey.UsePetInscriptionId, thisSelect === CHARACTER_ENUM.potato ? "74f1c602036fc449a2323665ac88e922082e25149863f0fb0c97f83dec04b386i0" : "")
    GlobalSpaceEvent.SetDataValue<SpaceStatus>(GlobalDataKey.SpaceStatus, SpaceStatus.Game)
    // dispatch(setStorageMenu(STORAGE_MENU_ENUM.PLAY_MENU))
    onClose()
  }
  useEffect(() => {
    const spaceStatusKey = GlobalSpaceEvent.ListenKeyDataChange<string>(GlobalDataKey.UsePetInscriptionId, (value) => {
      dispatch(setUsedCharacter(value.length > 0 ? CHARACTER_ENUM.potato : CHARACTER_ENUM.character))
    })
    return () => {
      GlobalSpaceEvent.RemoveListener(GlobalDataKey.SpaceStatus, spaceStatusKey)
    }
  }, []);

  return <Modal visible={visible} onClose={onClose} title={"Character Selection"} width={"624px"} zIndex={9}>
    <CharacterSelectionModalView>
      <div className="modal-grid">
        <div className={classNames({
          "modal-grid-item": true,
          "active": thisSelect === CHARACTER_ENUM.character
        })} onClick={() => {
          setThisSelect(CHARACTER_ENUM.character)
        }}>
          <img src={RoleImg.src} alt=""/>
        </div>
        <div className={classNames({
          "modal-grid-item": true,
          "active": thisSelect === CHARACTER_ENUM.potato,
          "disabled": false
        })} onClick={() => {
          setThisSelect(CHARACTER_ENUM.potato)
        }}>
          <img src={PotatoImg.src} alt=""/>
        </div>
      </div>
      <button onClick={onSelect}>Confirm</button>
  </CharacterSelectionModalView>
  </Modal>
}
