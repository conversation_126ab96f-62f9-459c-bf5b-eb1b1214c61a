"use client";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  QuestionH<PERSON>oryBox,
  QuestionHistoryModalView,
} from "./style";
import Modal from "../../../BasicComponents/Modal";
import CloseSvg from "/public/image/basic/close.png";
import HistoryTitle from "/public/image/assistant/history-title.png";
import { IAppState, IAssistantQuestionHistory } from "@root/constant/type";
import { useEffect, useState } from "react";
import { getAssistantChatHistory } from "@root/server";
import toast from "react-hot-toast";
import { useSelector } from "react-redux";
import HistoryList from "./components/HistoryList";
import HistoryContent from "./components/HistoryContent";
import Image from "next/image";
// import assistantHistory from "@/public/data/assistantHistory";
export default function QuestionHistoryModal({
  visible,
  onClose,
}: {
  visible: boolean;
  onClose: () => void;
}) {
  const { btcAddress } = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const [selectedHistory, setSelectedHistory] =
    useState<IAssistantQuestionHistory | null>(null);
  const [historyList, setHistoryList] = useState<IAssistantQuestionHistory[]>(
    []
  );
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const getHistory = () => {
    if (btcAddress && visible) {
      setIsLoading(true);
      getAssistantChatHistory({
        pageNo: 1,
        pageSize: 50,
      }).then((res) => {
        if (res.data.code === 1) {
          setHistoryList(res.data.data || []);
        } else {
          toast.error(res.data.msg);
        }
      }).finally(() => {
        setIsLoading(false);
      });
    }
  };
  useEffect(() => {
    getHistory();
  }, [btcAddress, visible]);

  // useEffect(() => {
  //   if (assistantHistory.length > 0) {
  //     setHistoryList(assistantHistory);
  //   }
  // }, [assistantHistory]);

  const handleSelectHistory = (history: IAssistantQuestionHistory) => {
    setSelectedHistory(history);
  };
  useEffect(() => {
    if (historyList.length > 0) {
      setSelectedHistory(historyList[0]);
    }
  }, [historyList]);

  return (
    <Modal
      visible={visible}
      onClose={onClose}
      zIndex={99}
      emptyOnly={true}
      width="1154px"
    >
      <QuestionHistoryModalView>
        <Image
          src={CloseSvg.src}
          alt=""
          onClick={onClose}
          className="close-btn"
          width={56}
          height={56}
        />
        <Image
          src={HistoryTitle.src}
          alt=""
          className="history-title"
          width={358}
          height={84}
        />
        <QuestionHistoryBox>
          <HistoryList
            historyList={historyList}
            selectedHistory={selectedHistory}
            onSelectHistory={handleSelectHistory}
            address={btcAddress}
          />
          
          <HistoryContentWrapper>
            <HistoryContent
              history={selectedHistory}
              historyList={historyList}
              address={btcAddress}
              isLoading={isLoading}
            />
          </HistoryContentWrapper>
        </QuestionHistoryBox>
      </QuestionHistoryModalView>
    </Modal>
  );
}
