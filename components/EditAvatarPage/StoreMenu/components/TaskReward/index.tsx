import Dialog from "@/commons/Dialog";
import {forwardRef, useImperativeHandle, useMemo, useRef, useState} from "react";
import styled from "styled-components";
import rewardBg from "/public/image/reward-bg.png";
import rewardTitle from "/public/image/claim-rewards.png";
// import rewardAxe1 from "/public/image/rewardsAxe-1.png";
// import potato from "/public/image/wangcai.png";
import Image from "next/image";
import StartButton from "@/components/EventRules/components/StartButton";
import Star1 from "/public/image/star1.svg";
import Star2 from "/public/image/star2.svg";

interface RewardsProps {
  onCloseCallback?: () => void;
  onClaimReward?: () => void;
}

// 定义暴露给父组件的引用接口
export interface RewardsRef {
  open: (rewardInfo: RewardInfo) => void;
  close: () => void;
  setLoading: (loading: boolean) => void;
}

interface RewardInfo {
  title: string;
  rewardList: any[];
  count: number;
}

const Container = styled.div`
  width: 744px;
  height: 440px;
  background-image: url(${rewardBg.src});
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
  // 定义一个伪类affter， 这个元素用于弹窗背景，需要绘制一些淡黄色的朦胧感，主要是围绕着整个父元素，层级要最低
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(236, 169, 69, 0.9); /* 淡黄色半透明背景 */
    filter: blur(20px); /* 添加模糊效果增加朦胧感 */
    z-index: -1; /* 确保在底层 */
    border-radius: 40px;
    pointer-events: none; /* 确保不影响交互 */
    transform: translateZ(0);
  }
  .reward-title {
    width: 544px;
    height: 120px;
    background-image: url(${rewardTitle.src});
    background-size: initial;
    background-repeat: no-repeat;
    position: absolute;
    top: -6%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 10;
    background-position: bottom;
  }

  .rewards-content {
    width: 100%;
    height: calc(100% - 100px);
    display: flex;
    align-items: center;
    justify-content: start;
    padding-top: 10%;
    flex-direction: column;
    gap: 6px;
    & > p {
      color: #140f08;
      margin: 0;
      padding: 0;
    }
    .rewards-content-item {
      font-size: 20px;
      word-wrap: break-word;
      width: 100%;
      text-align: center;
      color: #140f08;
      font-weight: 900;
      text-shadow: 0 0 0.5px #000;
      -webkit-text-stroke: 0.5px #000;
    }
    .following {
      text-shadow: none;
      width: 100%;
      -webkit-text-stroke: 0px;
    }
  }
  .star1 {
    position: absolute;
    top: 10%;
    left: -4%;
  }
  .star2 {
    position: absolute;
    top: 25%;
    left: -6%;
  }
  .group-jinbi {
    position: absolute;
    bottom: -5%;
    right: -8%;
    background: url("/image/start-group.png");
    background-size: initial;
    background-position: center;
    background-repeat: no-repeat;
    width: 166px;
    height: 192px;
  }
`;
const RewardNumber = styled.div`
  border-radius: 30px;
  transition: all 0.3s ease;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 20px 0px;
  box-sizing: border-box;
  width: 80%;
  height: 150px;
  /* 更强烈边框内凹立体效果 */
  box-shadow: inset 0 0px 20px rgba(0, 0, 0, 0.15);

  /* 使用渐变背景增强立体感 */
  background-color: #f7e7cd;

  /* 去除所有边框 */
  border: none;
  outline: none;
  gap: 10px;
`;

const RequirementImage = styled.div<{}>`
  width: 100px;
  height: 100px;
  border-radius: 16px;
  background-color: #faf3e5;
  border: 1px solid #c2b8a2;
  transition: transform 0.3s, box-shadow 0.3s;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.3);
  position: relative;
  .material-icon {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 6;
  }
  .reward-count {
    position: absolute;
    bottom: 0;
    left: 7px;
    color: #A58061;
    border-radius: 12px;
    padding: 0 4px;
    font-size: 12px;
    font-weight: 600;
    z-index: 10;
  }
`;

// 修改为forwardRef组件
const RewardsComponent = forwardRef<RewardsRef, RewardsProps>((props, ref) => {
  const { onCloseCallback, onClaimReward } = props;
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [rewardInfo, setRewardInfo] = useState<RewardInfo>({
    title: "",
    rewardList: [],
    count: 0,
  });

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    open: (info: RewardInfo) => {
      setRewardInfo(info);
      setIsOpen(true);
    },
    close: () => {
      setIsOpen(false);
    },
    setLoading: (loading: boolean) => {
      setIsLoading(loading);
    }
  }));

  const onShare = () => {
    if (onClaimReward) {
      // 不自动关闭，让父组件控制loading状态和关闭时机
      onClaimReward();
    } else {
      // 如果没有提供onClaimReward，则直接关闭
      setIsOpen(false);
      onCloseCallback?.();
    }
  };

  const handleClose = () => {
    setIsOpen(false);
    onCloseCallback?.();
  };

  return (
    <Dialog isOpen={isOpen} onClose={handleClose}>
      <Container>
        <div className="reward-title"></div>
        <Image
          src={Star1}
          alt="star1"
          className="star1"
          width={60}
          height={60}
        />
        <Image
          src={Star2}
          alt="star2"
          className="star2"
          width={34}
          height={34}
        />
        <div className="rewards-content">
          <p className="rewards-content-item">Congratulations!</p>
          <p className="rewards-content-item">
            You have completed {rewardInfo.title},
          </p>

          <p className="rewards-content-item following">
            Please claim your reward
          </p>
          <RewardNumber>
            {rewardInfo.rewardList.map((item, index) => (
              <RequirementImage key={index}>
                <Image src={item.url} alt="" width={100} height={100}/>
                {item.quantity > 0 && (
                  <div className="reward-count">{item.quantity}</div>
                )}
              </RequirementImage>
            ))}
          </RewardNumber>
          <StartButton text="Claim" onClick={onShare} loading={isLoading} />
        </div>
        <div className="group-jinbi" />
      </Container>
    </Dialog>
  );
});

// 创建一个新的hook，返回ref和组件
function useRewards(props: RewardsProps = {}) {
  const rewardsRef = useRef<RewardsRef>(null);
  
  const Rewards = useMemo(() => {
    // 使用forwardRef创建的组件
    return () => <RewardsComponent ref={rewardsRef} {...props} />;
  }, [props]);

  return { 
    Rewards, 
    rewardsRef,
    // 为方便使用，也可以直接暴露方法
    setLoading: (loading: boolean) => rewardsRef.current?.setLoading(loading),
    openRewards: (info: RewardInfo) => rewardsRef.current?.open(info),
    closeRewards: () => rewardsRef.current?.close()
  };
}

export default useRewards;
