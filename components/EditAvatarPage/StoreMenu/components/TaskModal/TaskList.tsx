import styled from "styled-components";
import TaskCard, { TaskSubItem } from "../TaskCard";
import Masonry from "react-masonry-css";
import { FormattedTask } from "../TaskModal";
import { claimTaskReward, getTaskList } from "@/server";
import toast from "react-hot-toast";
import { useTaskContext } from "@/contexts/TaskContext";
import useRewards from "../TaskReward";
import { useRef } from "react";

// 容器样式
const TaskListContainer = styled.div`
  flex: 80%;
  /* overflow-y: auto; */
  overflow: hidden;
  box-sizing: border-box;
  padding: 14px;
  border-radius: 12px;
  position: relative;
  background: url("/image/task/bg.png") no-repeat center center;
  background-size: 100% 100%;

  /* 自定义滚动条 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 245, 230, 0.5);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: #ff8316;
    border-radius: 3px;
  }

  .task-list-container {
    overflow-y: auto;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
  }

  /* 瀑布流样式 */
  .my-masonry-grid {
    display: flex;
    width: 100%;
    margin-left: -16px; /* 抵消列间距 */
    padding: 12px;
  }

  .my-masonry-grid_column {
    padding-left: 16px; /* 列间距 */
    background-clip: padding-box;
  }

  /* 每个卡片的底部间距 */
  .card-item {
    margin-bottom: 16px;
  }
`;

interface TaskListProps {
  tasks: FormattedTask[]; // 从父组件传递的已处理任务数据
  onClaim: () => void;
}

const TaskList = ({ tasks = [], onClaim }: TaskListProps) => {
  // 瀑布流的列数配置
  const breakpointColumns = {
    default: 2, // 默认2列
    767: 1, // 小于767px时变成1列
  };

  const { syncTaskProgress, incentivesConfig, removeTask } = useTaskContext();

  const taskIdRef = useRef<string>("");

  const { Rewards, rewardsRef, setLoading } = useRewards({
    onClaimReward,
  });

  async function onClaimReward() {
    try {
      setLoading(true);
      const res = await claimTaskReward({ id: taskIdRef.current });
      if (res.data.code === 1) {
        onClaim();
        toast.success("Claimed reward successfully!");
        const result = await getTaskList();
        if (result.data.code === 1) {
          const list = result.data.data;
          syncTaskProgress(list, incentivesConfig);
          removeTask(taskIdRef.current);
          taskIdRef.current = "";
          setLoading(false);
          rewardsRef.current?.close();
        }
      } else {
        toast.error(res.data.msg);
        taskIdRef.current = "";
      }
    } catch (error) {
      console.log("Error claiming reward:", error);
    }
  }

  // const { syncTaskProgress, incentivesConfig } = useTaskContext();

  // 处理领取奖励
  const handleClaimReward = async (task: FormattedTask) => {
    taskIdRef.current = task.id;
    rewardsRef.current?.open({
      title: task.title,
      rewardList: task?.rewards ?? [],
      count: 1,
    });
  };

  // 处理追踪任务
  const handleTrackTask = (taskId: string) => {
    console.log("Tracking task:", taskId);
    // 这里应该调用API来追踪任务
  };


  return (
    <>
      <TaskListContainer>
        <div className="task-list-container">
          {tasks.length > 0 ? (
            <Masonry
              breakpointCols={breakpointColumns}
              className="my-masonry-grid"
              columnClassName="my-masonry-grid_column"
            >
              {tasks.map((task) => (
                <div key={task.id} className="card-item">
                  <TaskCard
                    id={task.id}
                    title={task.title}
                    timeRemaining={task.timeRemaining}
                    newTag={task.newTag}
                    taskItems={task.taskItems}
                    status={task.status}
                    onClaim={() => handleClaimReward(task)}
                    onTrack={() => handleTrackTask(task.id)}
                    rewards={task.rewards}
                    taskDesc={task.taskDesc}
                    taskType={task.taskType}
                  />
                </div>
              ))}
            </Masonry>
          ) : (
            <div
              style={{ textAlign: "center", padding: "40px 0", color: "#999" }}
            >
              No tasks available in this category
            </div>
          )}
        </div>
      </TaskListContainer>
      <Rewards />
    </>
  );
};

export default TaskList;
