import { INVENTORY_TYPE_ENUM } from "@/constant/type";
import StaggeredAnimation from "@/commons/StaggeredAnimation";
import ContentItem from "../ContentItem";
import { Dispatch, SetStateAction, useEffect, useState } from "react";
import { setEquipmentShortcut } from "../../../../server";
import toast from "react-hot-toast";
import useBagInventory from "../../../../hooks/useBagInventory";

interface OutfitListProps {
  bagInventoryList: any[];
  isVisible: boolean;
  setIsDragging: Dispatch<SetStateAction<boolean>>;
}

const OutfitList = ({
  bagInventoryList,
  isVisible,
  setIsDragging,
}: OutfitListProps) => {
  // 获取fetchAllData函数用于刷新数据
  const { fetchAllData } = useBagInventory(false);
  const [isDragOver, setIsDragOver] = useState(false);

  // 创建一个固定大小的格子数组，总共20个格子
  const TOTAL_SLOTS = 20;
  const slots = [...Array(TOTAL_SLOTS)].map((_, index) => {
    // 如果有对应的物品，使用物品数据；否则返回null表示空格子
    return index < bagInventoryList.length ? bagInventoryList[index] : null;
  });

  // useEffect(() => {
  //   console.log("slots ======", slots);
  // }, [slots]);

  // 处理从快捷栏拖拽物品到背包的逻辑
  const handleDropFromQuickBar = (e: any) => {
    e.preventDefault();
    const droppedItem = e.dataTransfer.getData("text/plain");
    try {
      const item = JSON.parse(droppedItem);
      // 确认是从快捷栏拖拽过来的物品
      if (item.shortcut !== null && item.shortcut !== "" && item.userItemId) {
        // 显示加载状态
        const loadingToast = toast.loading("Removing from quickbar...");
        // 调用API清除快捷键设置
        setEquipmentShortcut({
          userItemId: item.userItemId,
          shortcut: "0",
        })
          .then(async (res) => {
            if (res.data.code === 1) {
              // 成功后刷新数据
              await fetchAllData();
            } else {
              toast.error(res.data.msg || res.data.message[0], {
                duration: 6000,
              });
            }
          })
          .finally(() => {
            toast.dismiss(loadingToast);
          });
      }
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <div
      className={`inventory-list ${isDragOver ? "drag-over" : ""}`}
      onDragOver={(e) => {
        e.preventDefault();
        setIsDragOver(true);
      }}
      onDragLeave={() => {
        setIsDragOver(false);
      }}
      onDragExit={() => {
        setIsDragOver(false);
      }}
      onDrop={(e) => {
        e.preventDefault();
        setIsDragOver(false);
        handleDropFromQuickBar(e);
      }}
    >
      {slots.map((item, index) => (
        <StaggeredAnimation
          key={
            item
              ? item.userItemId || `item-${item.itemId}-${index}`
              : `empty-${index}`
          }
          index={index}
          isVisible={isVisible}
          initialScale={0.7}
          duration={0.5}
          bounceEffect={true}
        >
          {item ? (
            <ContentItem
              key={item.userItemId + "_" + index}
              src={item.icon}
              currentDurability={item.currentDurability}
              // num={item.quantity}
              check={false}
              questKey={
                item.shortcut !== "" && item.shortcut !== null
                  ? item.shortcut
                  : undefined
              }
              isNew={item.isNew}
              itemId={item.userItemId}
              draggable={item.type === INVENTORY_TYPE_ENUM.equipment}
              onDragStart={(e: any) => {
                setIsDragging(true);
                e.dataTransfer.setData("text/plain", JSON.stringify(item));
              }}
              onDragEnd={(e: any) => {
                setIsDragging(false);
                e.dataTransfer.clearData();
              }}
              showDetail={{
                name: item.name,
                description: "",
                type: item.type,
                maxDurability:
                  item.type === INVENTORY_TYPE_ENUM.equipment
                    ? item.maxDurability
                    : undefined,
                currentDurability:
                  item.type === INVENTORY_TYPE_ENUM.equipment
                    ? item.currentDurability
                    : undefined,
                // quantity: item.quantity,
                expirationTime: "1 Day",
                trait: item.isGlowWeapon ? "Not eligible for crafting" : undefined,
                quality: item.quality,
              }}
            />
          ) : (
            // 渲染空格子
            <ContentItem
              key={`empty-slot-${index}`}
              // 不设置src，显示为空格子
              check={false}
              disabled={false}
              style={{
                cursor: "default",
                background: "#FBF4E8", // 保持与有物品格子相同的背景色
              }}
            />
          )}
        </StaggeredAnimation>
      ))}
    </div>
  );
};

export default OutfitList;
