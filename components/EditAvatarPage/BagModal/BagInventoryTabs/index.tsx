import classNames from "classnames";
import { memo, useEffect, useMemo } from "react";
import Image from "next/image";

import Tab1Img from "/public/image/bag/tab-icon1.svg";
import Tab1ActiveImg from "/public/image/bag/tab-icon1-active.svg";
import Tab2Img from "/public/image/bag/tab-icon2.svg";
import Tab2ActiveImg from "/public/image/bag/tab-icon2-active.svg";
// import Tab3Img from "/public/image/bag/tab-icon3.png";
// import Tab3ActiveImg from "/public/image/bag/tab-icon3-active.png";

enum TAB_ENUM {
  THING_BAG = "THING_BAG",
  SYNTHETIC_BAG = "SYNTHETIC_BAG",
  MATERIAL_BAG = "MATERIAL_BAG",
}

interface IBagInventoryTabsProps {
  tab: TAB_ENUM;
  setTab: (tab: TAB_ENUM) => void;
  isBagMenu?: boolean;
  isSyntheticMenu?: boolean;
  isMaterialMenu?: boolean;
}

function BagInventoryTabs({
  tab,
  setTab,
  isBagMenu,
  isSyntheticMenu,
  isMaterialMenu,
}: IBagInventoryTabsProps) {
  // 设置背景图片
  // 如果不展示合成标签，则使用bg0，如果需要展示合成标签，则通过THING_BAG来判断
  const bg = useMemo(() => {
    if (!isSyntheticMenu && !isMaterialMenu) {
      return "bg0";
    }
    return tab === TAB_ENUM.THING_BAG ? "bg1" : "bg2";
  }, [isSyntheticMenu, isMaterialMenu, tab]);

  return (
    <div className={classNames("inventory-tabs", bg)}>
      {/* 物品背包标签 */}
      {isBagMenu && (
        <div
          className={tab === TAB_ENUM.THING_BAG ? "active tab" : ""}
          onClick={() => setTab(TAB_ENUM.THING_BAG)}
        >
          <Image
            src={tab === TAB_ENUM.THING_BAG ? Tab1ActiveImg.src : Tab1Img.src}
            alt=""
            width={64}
            height={64}
            loading="lazy"
            className="tab-icon-1"
            draggable={false}
          />
        </div>
      )}
      {/* 材料背包标签 */}
      {isMaterialMenu && (
        <div
          className={tab === TAB_ENUM.MATERIAL_BAG ? "active tab" : ""}
          onClick={() => setTab(TAB_ENUM.MATERIAL_BAG)}
        >
          <Image
            src={
              tab === TAB_ENUM.MATERIAL_BAG ? Tab2ActiveImg.src : Tab2Img.src
            }
            width={44}
            height={44}
            loading="lazy"
            alt=""
            className="tab-icon-2"
            draggable={false}
          />
        </div>
      )}
      {/* 合成背包标签 */}
      {/* {isSyntheticMenu && (
        <div
          className={tab === TAB_ENUM.SYNTHETIC_BAG ? "active tab" : ""}
          onClick={() => setTab(TAB_ENUM.SYNTHETIC_BAG)}
        >
          <Image
            src={
              tab === TAB_ENUM.SYNTHETIC_BAG ? Tab2ActiveImg.src : Tab2Img.src
            }
            alt=""
            width={64}
            height={64}
            loading="lazy"
            className="tab-icon-3"
            draggable={false}
          />
        </div>
      )} */}
    </div>
  );
}

export default memo(BagInventoryTabs);
