import {AccountInfoView} from "./style";
import UserLogoIcon from "/public/image/user-logo.png";
import WalletIcon from "/public/image/wallet.svg";
import {useDispatch, useSelector} from "react-redux";
import {IAppState} from "../../../constant/type";
import {toFormatAccount} from "../../../utils";
// @ts-ignore
import CopyToClipboard from "react-copy-to-clipboard";
import toast from "react-hot-toast";
import useBalance from "../../../hooks/useBalance";
import {useMemo, useState} from "react";
import {getRecommendedFees} from "../../../server";
import BigNumber from "bignumber.js";

export default function AccountInfo(){
  const {btcAddress} = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
  const {balance} = useBalance()
  // const [fractalBitcoinPrice, setFractalBitcoinPrice] = useState<number>(0)
  // useMemo(() => {
  //   getRecommendedFees().then((res:any) => {
  //     setFractalBitcoinPrice(res.data.data.fractalBitcoinPrice)
  //   })
  // }, [])
  // const balanceValue = useMemo(() => {
  //   return BigNumber(balance).multipliedBy(fractalBitcoinPrice).dp(2).toFormat()
  // }, [balance, fractalBitcoinPrice])

  return <AccountInfoView>
    <div className="user-logo">
      <img src={UserLogoIcon.src} alt=""/>
    </div>
    <div className="account-info">
      <div className="wallet-address">
        <img src={WalletIcon.src} alt=""/>
        <span>{toFormatAccount(btcAddress, 4, 5)}</span>
        <CopyToClipboard
          text={btcAddress}
          onCopy={() => {
            toast.success('copied!', {duration: 6000})
          }}>
            <svg width="32" height="33" viewBox="0 0 32 33" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M19.4613 13.0383V8.88458C19.4613 8.13044 18.8309 7.5 18.0767 7.5H8.3847C7.63057 7.5 7.00012 8.13044 7.00012 8.88458V18.5766C7.00012 19.3308 7.63057 19.9612 8.3847 19.9612H12.5384" stroke="#828282" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M23.6154 25.4993C24.3801 25.4993 25 24.8794 25 24.1147V14.4227C25 13.6685 24.3696 13.0381 23.6154 13.0381H13.9234C13.1693 13.0381 12.5388 13.6685 12.5388 14.4227V24.1147C12.5388 24.8689 13.1693 25.4993 13.9234 25.4993H23.6154Z" stroke="#828282" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
        </CopyToClipboard>
      </div>
      <h2 className="balance">
        {balance} FB
        {/*<span>(${balanceValue})</span>*/}
      </h2>
    </div>
  </AccountInfoView>
}
