import styled from "styled-components";

export const AccountInfoView = styled.div`
  display: flex;
  align-items: center;
  gap: 32px;

  .user-logo {
    width: 96px;
    height: 96px;
    border-radius: 50%;
    box-shadow: 0px 4px 24px 0px #********;
    border: 1px solid #FFFFFF;
    box-sizing: border-box;
    &>img{
      width: 100%;
      height: 100%;
    }
  }
  .account-info{
    .wallet-address{
      display: inline-flex;
      align-items: center;
      background: #F4F4F4;
      height: 48px;
      border-radius: 10px;
      padding: 0 16px;
      box-sizing: border-box;
      &>img{
        width: 32px;
        height: 32px;
      }
      &>span{
        border-left: 1px solid #828282;
        padding-left: 16px;
        margin-left: 16px;
      }
      svg{
        margin-left: 8px;
        cursor: pointer;
      }
    }
    &>.balance{
      font-family: JetBrainsMonoBold;
      font-size: 32px;
      font-weight: 700;
      line-height: 38.73px;
      text-align: left;
      color: #140F08;
      margin: 8px 0 0 0;
      &>span{
        font-family: JetBrainsMono;
        font-size: 24px;
        font-weight: 400;
        line-height: 29.05px;
        text-align: left;
        color: #615A57;
        margin-left: 34px;
      }
    }
  }
`
