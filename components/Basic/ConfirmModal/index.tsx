import Modal from "../../EditAvatarPage/BasicComponents/Modal";
import {ConfirmModalView} from "./style";
import CloseSvg from '/public/image/x.svg'

export default function ConfirmModal({visible, onClose,btnsStyle, title, content, onConfirm, confirmText, cancelText}: {
  visible: boolean,
  onClose: () => void,
  title: string,
  content: string,
  onConfirm: () => void,
  confirmText: string,
  cancelText: string,
  btnsStyle: any
}){
  return <Modal visible={visible} onClose={onClose} zIndex={99} emptyOnly={true} width="500px">
    <ConfirmModalView>
      <div className="modal-content">
        <h2>{title}</h2>
        <img src={CloseSvg.src} alt="" onClick={onClose} className="close-btn"/>
        <p className="content">{content}</p>
        <div className="btns" style={btnsStyle}>
          <button className="cancel-btn" onClick={onClose}>{cancelText}</button>
          <button className="confirm-btn" onClick={onConfirm}>{confirmText}</button>
        </div>
      </div>
    </ConfirmModalView>
  </Modal>
}
