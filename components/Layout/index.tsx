import Head from "next/head";
import React from "react";
import Favicon from "/public/image/ico.png";

import { WEBSITE_DOMAIN } from "../../constant";
import Loading from "../Loading";

// 临时测试用
const static_url = "https://static.satworld.io";
export default function Layout({
  children,
  metaVideoId,
  metaImage,
}: {
  children: React.ReactNode;
  metaVideoId?: string | null;
  metaImage?: string | null;
}) {
  const defMetaImage = `${WEBSITE_DOMAIN}/image/logo.png`;
  const image = metaImage || defMetaImage;
  return (
    <>
      <div>
        <Head>
          <title>SatWorld Avatar</title>
          <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0,user-scalable=no"
          />
          <link rel="shortcut icon" href={Favicon.src} type="image/x-icon" />

          <meta property="og:title" content="SatWorld" />
          <meta property="og:url" content={WEBSITE_DOMAIN} />
          <meta property="og:image" content={image} />
          <meta property="og:image:width" content="1200" />
          <meta property="og:image:height" content="675" />

          <meta name="description" content="SatWorld Avatar" />
          <meta name="twitter:card" content="summary_large_image" />
          {/*<meta name="twitter:site" content={CONFIG.title}></meta>*/}
          {/*<meta name="twitter:creator" content='@'></meta>*/}
          <meta property="twitter:title" content="SatWorld" />
          <meta property="twitter:image" content={image} />
          <meta name="twitter:image:alt" content={image} />
          <meta name="twitter:url" content={WEBSITE_DOMAIN} />
          {metaVideoId && (
            <>
              <meta
                name="twitter:player"
                content={
                  static_url +
                  "/bc1p27hldatmvy7z0da46lcx97mv48qaww67wq003xjvc5zl4xrcpgrqfcemj6/5e0b56de52fc15f92a3e450a8227bf5f/5e0b56de52fc15f92a3e450a8227bf5f.webm"
                }
              />
              {/*<meta name="twitter:player:width" content="640"/>*/}
              {/*<meta name="twitter:player:height" content="360"/>*/}
              {/*<meta name="twitter:player:stream" content="https://www.example.com/path/to/video/stream"/>*/}
            </>
          )}
        </Head>
      </div>

      {children}
      <Loading />
    </>
  );
}

// function Loading() {
//   const router = useRouter()
//   const {pageLoadingRate, isRunJumpLogic} = useSelector((state: { AppReducer: IAppState }) => state.AppReducer);
//   const [progress, setProgress] = useState(0)
//   const interval: any = useRef(null)

//   const needLoading = useMemo(() => {
//     if (['/', '/avatar'].includes(router.pathname)) {
//       return true
//     }
//     return false
//   }, [router])
//   useMemo(() => {
//     if (interval.current) {
//       clearInterval(interval.current);
//     }
//     if (!needLoading || !isRunJumpLogic) {
//       return () => clearInterval(interval.current)
//     }
//     if (pageLoadingRate === 0) {
//       setProgress(0)
//       // Progress increments gradually until 90
//       interval.current = setInterval(() => {
//         setProgress((prev) => {
//           const nextProgress = prev + 1;
//           return nextProgress < 90 ? nextProgress : 90;
//         });
//       }, 20); // Adjust interval speed as needed
//     } else if (pageLoadingRate === 100) {
//       // Progress quickly increments to 100
//       interval.current = setInterval(() => {
//         setProgress((p) => {
//           if (p === 100) {
//             clearInterval(interval.current);
//           }
//           return (p < 100 ? p + 1 : 100)
//         });
//       }, 20); // Faster increment to smoothly transition to 100
//     }

//     return () => {
//       if (interval.current) {
//         clearInterval(interval.current);
//       }
//     };
//   }, [pageLoadingRate, needLoading, isRunJumpLogic]);
//   if (!needLoading || (pageLoadingRate === 100 && progress === 100)) {
//     return null;
//   }
//   return <div style={{
//     position: "fixed",
//     left: 0,
//     top: 0,
//     width: '100vw',
//     height: '100vh',
//     zIndex: 10,
//     display: 'flex',
//     flexDirection: 'column',
//     alignItems: 'center',
//     justifyContent: 'center',
//     background: 'radial-gradient(circle, #fbd0ac, #f7ac6b)',
//     backdropFilter: 'blur(24px)'
//   }}>
//     <img src={LoadingGif.src} alt="" style={{width: "120px"}}/>
//     <p style={{color: "#000000", fontSize: "24px", fontFamily: "JetBrainsMono", marginTop: 0}}>Loading...</p>
//     <div
//       style={{width: '400px', height: "8px", border: '1px solid #000000', boxSizing: 'border-box', marginTop: "20px"}}>
//       <div style={{width: `${progress}%`, height: "100%", background: "#000000", position: "relative"}}>
//         <span style={{
//           position: "absolute",
//           bottom: 0,
//           right: 0,
//           transform: "translate(50%, 100%)",
//           color: "#000000",
//           fontSize: "20px",
//           fontFamily: "JetBrainsMono"
//         }}>{progress}%</span>
//       </div>
//     </div>
//   </div>
// }
