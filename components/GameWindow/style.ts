import styled from "styled-components";

export const GameWindowView = styled.div`
    width: 100vw;
    height: 100vh;
    display: flex;
    overflow: hidden;
    position: relative;

    #render-target-game {
        flex: 1;
        height: 100%;
        width: 100%;
        background: #000000;
        display: flex;
        align-items: center;
        justify-content: center;
        position: fixed;
    }
    .controlKeys {
        position: absolute;
        width: 20rem;
        left: 50%;
        margin-left: -10rem;
        bottom: 13%;
        user-select: none;
        -moz-user-select: none;
        -webkit-user-drag: none;
        -webkit-user-select: none;
        -ms-user-select: none;
    }
`
