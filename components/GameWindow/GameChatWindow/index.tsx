"use client";

import styled from "styled-components";
import SendImg from "/public/image/recording/send.svg";
import {useEffect, useReducer, useRef, useState} from "react";
import {GetMyPlayer} from "@/world/Character/MyPlayer";
import {GetGameNetWork} from "@/world/hooks/useNetWork";
// import { trim } from "lodash";
import {trim} from "es-toolkit";
import EmojiButton, {EmojiButtonRef} from "@/commons/EmojiButton";
import {KeyPressUtil} from "@/world/Global/GlobalKeyPressUtil";
import {ChatManager} from "@/model/Chat/ChatManager";
import {ChatTabType} from "@/model/Chat/ChatType";

// 整个聊天区域的容器，包含感应区和聊天窗口
const ChatContainer = styled.div`
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 150px; // 足够高以包含聊天窗口和感应区
  z-index: 1;
  pointer-events: none; // 不阻止下方游戏区域的交互
`;

// 聊天窗口容器
const ChatWindowWrapper = styled.div<{ show: boolean }>`
  width: 588px;
  position: absolute;
  left: 50%;
  bottom: ${(props) => (props.show ? "16px" : "-50px")};
  transform: translateX(-50%);
  transition: bottom 0.3s ease;
  display: flex;
  z-index: 2;
  pointer-events: auto; // 恢复交互

  .history-btn {
    align-self: flex-end;
    width: 72px;
    height: 72px;
    cursor: pointer;
  }

  .question-box {
    flex: 1;

    .question-history {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      & > span {
        padding: 9px 16px;
        word-break: break-all;
        font-family: Inter;
        font-weight: 400;
        font-size: 12px;
        line-height: 14.52px;
        color: #ffffff;
        background: #795f49;
        border-radius: 12px;
        cursor: pointer;
      }
    }

    .question-ask {
      margin-top: 8px;
      display: flex;
      align-items: center;
      background: #ffffff;
      border: 1px solid #ac9d83;
      box-shadow: 0px 4px 24px 0px #00000059;
      border-radius: 24px;
      padding: 5px 16px;
      box-sizing: border-box;
      height: 70px;
      gap: 2px;

      .question-ask-input {
        flex: 1;
        overflow: hidden;
        height: 100%;

        & > input {
          width: 100%;
          height: 100%;
          padding: 0;
          outline: none;
          border: 0;
          font-family: Inter;
          font-weight: 400;
          font-size: 18px;
          line-height: 21.78px;
          color: #140f08;

          &::placeholder {
            color: #686663;
          }
        }
      }

      button {
        width: 40px;
        height: 40px;
        outline: none;
        border-radius: 10px;
        border: 0;
        background: #ff8316;
        box-shadow: 0px -2.5px 0px 0px #00000040 inset;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        & > img {
          width: 20px;
          height: 20px;
        }

        &[disabled] {
          cursor: not-allowed;
          background: #c9b7a5;
          box-shadow: 0px -2.5px 0px 0px #00000040 inset;
        }

        &.loading {
          & > img {
            animation: loading-ano 1s infinite;
          }
        }
      }
    }
  }
`;

// 添加倒计时样式
const CooldownText = styled.span`
  font-size: 18px;
  font-weight: bold;
  color: white;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`;

export default function GameChatWindow() {
  const myPlayer = GetMyPlayer();
  const gameNetWork = GetGameNetWork();
  const inputRef = useRef<HTMLInputElement>(null);
  const [isEnterRoom, setIsEnterRoom] = useState(false);
  const [inputValue, setInputValue] = useState("");
  // 使用 useRef 替代所有状态
  const isHoveredRef = useRef<boolean>(false);
  const isFocusedRef = useRef<boolean>(false);
  const showChatRef = useRef<boolean>(false);
  const emojiButtonRef = useRef<EmojiButtonRef>(null);

  // 添加发送冷却状态
  const [isOnCooldown, setIsOnCooldown] = useState(false);
  const [cooldownSeconds, setCooldownSeconds] = useState(0);
  const cooldownTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 创建一个forceUpdate函数，通过useReducer实现
  const [, forceUpdate] = useReducer((x) => x + 1, 0);

  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // 更新显示状态的辅助函数
  const updateShowState = () => {
    const newShowState = isHoveredRef.current || isFocusedRef.current;
    if (showChatRef.current !== newShowState) {
      showChatRef.current = newShowState;
      forceUpdate(); // 强制组件重新渲染
    }
  };

  // 开始冷却倒计时
  const startCooldown = () => {
    setIsOnCooldown(true);
    setCooldownSeconds(3);

    // 清除任何现有的冷却计时器
    if (cooldownTimerRef.current) {
      clearInterval(cooldownTimerRef.current);
    }

    // 创建新的冷却计时器，每秒减少冷却时间
    cooldownTimerRef.current = setInterval(() => {
      setCooldownSeconds(prev => {
        if (prev <= 1) {
          // 冷却结束
          clearInterval(cooldownTimerRef.current!);
          setIsOnCooldown(false);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // 清理冷却计时器
  useEffect(() => {
    return () => {
      if (cooldownTimerRef.current) {
        clearInterval(cooldownTimerRef.current);
      }
    };
  }, []);

  const onAsk = () => {
    if (!isOnCooldown && inputValue) {
      // 过滤掉空消息
      if (trim(inputValue) === "") {
        return;
      }
      // 超过100个字符不允许发送
      if (inputValue.length > 100) {
        return;
      }
      ChatManager.getInstance().sendChatMessage(myPlayer.btcAddress, ChatTabType.Room, inputValue, '')
      setInputValue("");

      // 触发发送冷却
      startCooldown();

      // 发送消息后立即让输入框失去焦点
      // if (inputRef.current) {
      //   inputRef.current.blur();
      // }
    }
  };

  useEffect(() => {
    gameNetWork.watchRoomStatus((data) => {
      setIsEnterRoom(data.isEnterRoom);
    });
  }, []);

  // 鼠标移入事件处理
  const handleMouseEnter = () => {
    // 清除任何现有的隐藏计时器
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }

    isHoveredRef.current = true;
    updateShowState();
  };

  // 鼠标移出事件处理
  const handleMouseLeave = () => {
    // 只有当输入框没有焦点时才隐藏
    if (!isFocusedRef.current) {
      timeoutRef.current = setTimeout(() => {
        isHoveredRef.current = false;
        updateShowState();
        timeoutRef.current = null;
      }, 300);
    }
  };

  // 组件卸载时清除计时器
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  return (
    <>
      {isEnterRoom && (
        <ChatContainer
          ref={containerRef}
          onMouseEnter={handleMouseEnter}
          onMouseLeave={handleMouseLeave}
        >
          <ChatWindowWrapper show={showChatRef.current}>
            <div className="question-box">
              <div className="question-ask">
                <div className="question-ask-input">
                  <input
                    type="text"
                    placeholder="Please enter chat message"
                    ref={inputRef}
                    maxLength={100}
                    value={inputValue}
                    onChange={(e) => setInputValue(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" && !isOnCooldown) {
                        onAsk();
                      }
                    }}
                    onFocus={() => {
                      // 禁止玩家控制
                      KeyPressUtil.setEnable(false);
                      isFocusedRef.current = true;
                      updateShowState();
                    }}
                    onBlur={() => {
                      // 开启玩家控制
                      KeyPressUtil.setEnable(true);
                      isFocusedRef.current = false;

                      // 输入框失去焦点后，直接启动隐藏计时器
                      timeoutRef.current = setTimeout(() => {
                        // 只有当鼠标不在容器内才隐藏
                        if (!containerRef.current?.matches(":hover")) {
                          isHoveredRef.current = false;
                          updateShowState();
                        }
                        timeoutRef.current = null;
                      }, 300);
                    }}
                  />
                </div>
                <EmojiButton ref={emojiButtonRef} onChangeEmoji={emoji => {
                  if (emoji) {
                    setInputValue(prevValue => prevValue + emoji)
                  }
                }} />
                <button
                  onClick={onAsk}
                  disabled={isOnCooldown}
                  title={isOnCooldown ? `Wait ${cooldownSeconds}s` : "Send message"}
                >
                  {isOnCooldown ? (
                    <CooldownText>{cooldownSeconds}</CooldownText>
                  ) : (
                    <img src={SendImg.src} alt="" />
                  )}
                </button>
              </div>
            </div>
          </ChatWindowWrapper>
        </ChatContainer>
      )}
    </>
  );
}
