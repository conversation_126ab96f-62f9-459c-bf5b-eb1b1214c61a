import styled from "styled-components";
import { useState } from "react";

const ToggleSwitchContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin-top: 30px;
  margin-bottom: 30px;
`;

const TabsRow = styled.div`
  display: flex;
  gap: 0;
  margin-bottom: 4px;
`;

const TabButton = styled.button<{ active: boolean }>`
  border: none;
  outline: none;
  background: ${({ active }) => (active ? "#fc7922" : "#c1af9c")};
  color: #fff;
  font-size: 16px;
  font-weight: 500;
  border-radius: 16px;
  height: 48px;
  padding: 2px 40px;
  margin-right: -26px;
  z-index: ${({ active }) => (active ? 2 : 1)};
  position: relative;
  cursor: pointer;
  transition: background 0.2s, color 0.2s;
  box-shadow: ${({ active }) => (active ? "0 4px 0 0 #d9c4a3" : "none")};
`;

const Desc = styled.div`
  font-size: 15px;
  color: #14110a;
  margin-top: 6px;
  margin-bottom: 2px;
  .desc-content {
    display: flex;
    align-items: center;
    gap: 4px;
  }
`;

const MenuList = [
  {
    title: "Generate Drops",
    id: 1,
  },
  {
    title: "Equally Distributed",
    id: 2,
  },
];

const PlayersNumber = styled.span`
  color: #fc7922;
  font-weight: 700;
`;

interface ToggleSwitchProps {
  value: number;
  onTabChange: (id: number) => void;
  players: number;
}

const ToggleSwitch = ({ value, onTabChange, players }: ToggleSwitchProps) => {
  const handleTabClick = (id: number) => {
    if (id !== value) {
      onTabChange(id);
    }
  };

  return (
    <ToggleSwitchContainer>
      <TabsRow>
        {MenuList.map((tab) => (
          <TabButton
            key={tab.id}
            active={value === tab.id}
            onClick={() => handleTabClick(tab.id)}
          >
            {tab.title}
          </TabButton>
        ))}
      </TabsRow>
      <div className="content">
        <Desc>
          {value === 1 ? (
            <>
              Enter token and amount to airdrop
              <>
                {" "}
                (
                <span style={{ color: "#fc7922", fontWeight: 600 }}>
                  {players || 0}
                </span>{" "}
                players)
              </>
            </>
          ) : (
            "Enter token and recipients for airdrop."
          )}
        </Desc>
      </div>
    </ToggleSwitchContainer>
  );
};

export default ToggleSwitch;
