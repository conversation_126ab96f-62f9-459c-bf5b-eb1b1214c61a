import {useDispatch, useSelector} from "react-redux";
import {IAppState} from "@/constant/type";
import {createParams, getLocalSession, rsaEncrypt,} from "@/utils";
import toast from "react-hot-toast";
import {receiveActivityItem, userActionStone, userActionTree} from "@/server";
import {useResourceList} from "@/hooks/useResourceList";
import {TreeConfig} from "@/world/Config/TreeConfig";
import {StoneConfig} from "@/world/Config/StoneConfig";
import {Events} from "@/utils/clientEvents";
import {setRandomEventResult, setUserBasicInfo} from "@/store/app";
import useBagInventory from "@/hooks/useBagInventory";

const useHandItem = () => {
  const {btcAddress, userBasicInfo} = useSelector(
    (state: { AppReducer: IAppState }) => state.AppReducer
  );
  const {addNewItem, updateDurability} = useBagInventory()
  const {refreshRockList, refreshTreeList} = useResourceList({
    autoFetch: false,
  });

  const dispatch = useDispatch();

  // 领取工具
  const onReceiveTool = async (token: string, singleType: string) => {
    try {
      // toast("Receiving Pickaxe...");
      const params = createParams(
        btcAddress,
        "/user-action/receive-activity-item"
      );
      const encrypted = rsaEncrypt(params);
      const {sessionId} = getLocalSession(btcAddress);

      if (!encrypted || !sessionId) {
        toast.error("Get Session failed");
        return;
      }

      // 创建请求头对象，包含 CloudFlare 验证令牌
      const headers = {
        turnstile: token, // 添加 CloudFlare 验证令牌
        sw: encrypted,
      };

      const res = await receiveActivityItem(headers);

      const {code, data, msg} = res.data;
      if (code === 1) {
        const {receivedCount, totalCount, userItemInfo} = data;
        if (userItemInfo) {
          addNewItem(userItemInfo);
        }

        if (userBasicInfo) {
          let toolConfig = {
            ...userBasicInfo.toolConfig,
            receivedCount,
            totalCount,
          };
          //更新一下背包数据
          dispatch(
            setUserBasicInfo({
              ...userBasicInfo,
              toolConfig,
            })
          );
        }
      } else {
        toast.error(msg || "Claim Failed");
      }
    } catch (error) {
      console.error("Claim Failed:", error);
      toast.error(error as string);
    }
  };

  //上报挖矿积分
  const reportScorePickAxe = async (id: string, userItemId: string) => {
    if (!btcAddress) {
      toast.error("Connect your wallet");
      return;
    }
    try {
      const params = createParams(btcAddress, "/user-action/stone-action");
      const encrypted = rsaEncrypt(params);
      const headers = {
        sw: encrypted,
        address: btcAddress,
        session: getLocalSession(btcAddress).sessionId,
      };

      const res = await userActionStone(
        {
          targetId: id,
          userItemId: userItemId,
        },
        headers
      );

      const {code, msg, data} = res.data;

      if (code === 1) {
        // toast.success("Report Score Success");
        const {userItemId, currentDurability, tag, randomEventResult} = data;

        dispatch(setRandomEventResult(randomEventResult));

        if (userItemId) {
          updateDurability(userItemId, currentDurability)
        }
        Events.emitItemCollected("/image/t2-2.png", 1);

        // 检查更新后是否还有存活的树木
        const remainingLivingStoneCount =
          StoneConfig.getInstance().getAliveStoneCount();
        if (remainingLivingStoneCount === 0) {
          // 如果没有存活的树木了，立即获取新的树木列表
          await refreshRockList();
        }
      } else {
        await refreshRockList();
        toast.error(msg);
      }
    } catch (error) {
      await refreshRockList();
      toast.error("Failure to mining");
    }
  }

  const cutTree = async (treeId: string, userItemId: string) => {
    if (!btcAddress) {
      toast.error("Connect your wallet");
      return;
    }
    try {
      const params = createParams(btcAddress, "/user-action/tree-action");
      const encrypted = rsaEncrypt(params);
      const headers = {
        sw: encrypted,
        address: btcAddress,
        session: getLocalSession(btcAddress).sessionId,
      };

      const res = await userActionTree(
        {
          targetId: treeId,
          userItemId: userItemId,
        },
        headers
      );

      const {code, msg, data} = res.data;

      if (code === 1) {
        const {userItemId, currentDurability, tag, randomEventResult} = data;

        dispatch(setRandomEventResult(randomEventResult));
        if (userItemId) {
          updateDurability(userItemId, currentDurability)

        }
        Events.emitItemCollected("/image/t2-1.png", 1);

        // 检查更新后是否还有存活的树木
        const remainingLivingTreeCount =
          TreeConfig.getInstance().getAliveTreeCount();
        if (remainingLivingTreeCount === 0) {
          // 如果没有存活的树木了，立即获取新的树木列表
          await refreshTreeList();
        }
      } else {
        toast.error(msg);
        await refreshTreeList();
      }
    } catch (error) {
      toast.error("Failure to cut tree");
      await refreshTreeList();
    } finally {
    }
  }

  return {
    cutTree,
    onReceiveTool,
    reportScorePickAxe,
  };
};

export default useHandItem;
